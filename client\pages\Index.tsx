import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import {
  Github,
  Linkedin,
  Mail,
  ExternalLink,
  Phone,
  MapPin,
  ArrowRight,
  GraduationCap,
  Briefcase,
  FolderOpen,
  Heart,
  Code,
  Palette,
  Server,
  Zap,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Footer from "@/components/Footer";

export default function Index() {
  // Enhanced Hero Section
  const HeroSection = () => (
    <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(56,189,248,0.1),transparent_50%)]" />

      {/* Animated geometric shapes */}
      <div className="absolute right-10 top-1/2 -translate-y-1/2 hidden lg:block">
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 90, 180, 270, 360],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="w-32 h-32 bg-sky-400/20 border border-sky-400/30 rounded-lg backdrop-blur-sm"
        />
        <motion.div
          animate={{
            y: [20, -20, 20],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
          className="w-16 h-16 bg-blue-500/20 border border-blue-500/30 rounded-full mt-4 ml-8 backdrop-blur-sm"
        />
      </div>

      <div className="max-w-6xl mx-auto px-6 py-20 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="mb-6"
          >
            <span className="text-4xl">👋</span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            Hi, I'm{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-500 bg-clip-text text-transparent">
              Swastik Saumya
            </span>
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-300 mb-4 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            Beginner Web Developer | BCA Student
          </motion.p>

          <motion.p
            className="text-lg text-gray-400 mb-8 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            I build responsive and animated websites with modern technologies
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button
              size="lg"
              className="bg-sky-400 hover:bg-sky-500 text-white"
              onClick={() =>
                document
                  .getElementById("projects")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              View My Work
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-sky-400 text-white hover:bg-sky-400 dark:border-sky-400 dark:text-white"
              onClick={() =>
                document
                  .getElementById("contact")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Get in Touch
            </Button>
          </motion.div>

          {/* Quick navigation cards */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-16 max-w-2xl mx-auto"
          >
            <Link to="/education">
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-center group cursor-pointer"
              >
                <GraduationCap className="w-8 h-8 mx-auto mb-3 text-sky-400" />
                <h3 className="font-semibold mb-2">Education</h3>
                <p className="text-sm text-gray-300">BCA Journey</p>
              </motion.div>
            </Link>

            <Link to="/internship">
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-center group cursor-pointer"
              >
                <Briefcase className="w-8 h-8 mx-auto mb-3 text-sky-400" />
                <h3 className="font-semibold mb-2">Internship</h3>
                <p className="text-sm text-gray-300">Work Experience</p>
              </motion.div>
            </Link>

            <Link to="/projects">
              <motion.div
                whileHover={{ y: -5, scale: 1.02 }}
                className="p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-center group cursor-pointer"
              >
                <FolderOpen className="w-8 h-8 mx-auto mb-3 text-sky-400" />
                <h3 className="font-semibold mb-2">Projects</h3>
                <p className="text-sm text-gray-300">My Work</p>
              </motion.div>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );

  // About Section
  const AboutSection = () => (
    <section id="about" className="py-20 bg-white dark:bg-slate-900">
      <div className="max-w-6xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
            About Me
          </h2>
          <div className="w-20 h-1 bg-sky-400 mx-auto"></div>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="w-64 h-64 mx-auto rounded-full bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center text-white text-6xl font-bold shadow-2xl"
            >
              SS
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
              I'm a BCA student with a passion for front-end development. I love
              building smooth and responsive web apps using React, Tailwind CSS,
              and Framer Motion. Currently learning backend and building
              full-stack projects.
            </p>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse"></div>
                <span className="text-slate-700 dark:text-slate-300">
                  Frontend Development
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse"></div>
                <span className="text-slate-700 dark:text-slate-300">
                  React & TypeScript
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse"></div>
                <span className="text-slate-700 dark:text-slate-300">
                  Responsive Design
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse"></div>
                <span className="text-slate-700 dark:text-slate-300">
                  Learning Full-Stack
                </span>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 pt-4">
              {["Problem Solver", "Team Player", "Quick Learner"].map(
                (trait, index) => (
                  <motion.span
                    key={trait}
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="px-3 py-1 bg-sky-100 dark:bg-sky-900 text-sky-600 dark:text-sky-300 rounded-full text-sm"
                  >
                    {trait}
                  </motion.span>
                ),
              )}
            </div>

            <div className="flex space-x-4 pt-6">
              <Link to="/education">
                <Button
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <GraduationCap className="w-4 h-4" />
                  <span>My Education</span>
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
              <Link to="/internship">
                <Button
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Briefcase className="w-4 h-4" />
                  <span>My Experience</span>
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );

  // Skills Overview Section
  const SkillsSection = () => {
    const skillCategories = [
      {
        title: "Frontend",
        icon: <Palette className="w-6 h-6" />,
        skills: ["HTML", "CSS", "JavaScript", "React", "Tailwind CSS"],
        color: "from-pink-400 to-purple-500",
      },
      {
        title: "Backend",
        icon: <Server className="w-6 h-6" />,
        skills: ["Node.js", "Express", "MongoDB", "API Design"],
        color: "from-green-400 to-blue-500",
      },
      {
        title: "Tools",
        icon: <Zap className="w-6 h-6" />,
        skills: ["Git", "Figma", "Framer Motion", "TypeScript"],
        color: "from-yellow-400 to-orange-500",
      },
    ];

    return (
      <section className="py-20 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Skills Overview
            </h2>
            <div className="w-20 h-1 bg-sky-400 mx-auto mb-6"></div>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Technologies and tools I work with to bring ideas to life
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {skillCategories.map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <Card className="h-full shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-slate-700 border-none">
                  <CardHeader className="text-center">
                    <div
                      className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${category.color} rounded-2xl flex items-center justify-center text-white shadow-lg`}
                    >
                      {category.icon}
                    </div>
                    <CardTitle className="text-xl dark:text-white">
                      {category.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {category.skills.map((skill) => (
                        <Badge
                          key={skill}
                          variant="outline"
                          className="text-xs dark:border-slate-600 dark:text-slate-300"
                        >
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    );
  };

  // Projects Preview Section
  const ProjectsPreviewSection = () => {
    const featuredProjects = [
      {
        title: "Gait Recognition System",
        description: "CV & ML project using OpenCV for human gait analysis",
        tech: ["Python", "OpenCV", "ML"],
        category: "Machine Learning",
      },
      {
        title: "Resume Optimizer",
        description:
          "AI-powered tool for resume optimization and ATS compatibility",
        tech: ["React", "TypeScript", "AI"],
        category: "AI Tool",
      },
      {
        title: "Portfolio Website",
        description:
          "Modern, responsive portfolio with dark mode and animations",
        tech: ["React", "Tailwind", "Framer Motion"],
        category: "Web Development",
      },
    ];

    return (
      <section id="projects" className="py-20 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Featured Projects
            </h2>
            <div className="w-20 h-1 bg-sky-400 mx-auto mb-6"></div>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              A selection of projects showcasing my skills and learning journey
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {featuredProjects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
              >
                <Card className="h-full shadow-lg hover:shadow-xl transition-all duration-300 dark:bg-slate-800 border-none">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge className="bg-sky-100 dark:bg-sky-900 text-sky-600 dark:text-sky-300">
                        {project.category}
                      </Badge>
                      <Code className="w-5 h-5 text-slate-400" />
                    </div>
                    <CardTitle className="text-lg dark:text-white">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="dark:text-slate-300">
                      {project.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tech.map((tech) => (
                        <span
                          key={tech}
                          className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded text-xs"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <Link to="/projects">
              <Button size="lg" className="bg-sky-400 hover:bg-sky-500">
                <FolderOpen className="w-5 h-5 mr-2" />
                View All Projects
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    );
  };

  // Contact Section
  const ContactSection = () => (
    <section
      id="contact"
      className="py-20 bg-slate-900 dark:bg-slate-950 text-white"
    >
      <div className="max-w-6xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Get In Touch</h2>
          <div className="w-20 h-1 bg-sky-400 mx-auto mb-6"></div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            I'm always open to discussing new opportunities, exciting projects,
            and collaborations. Let's build something amazing together!
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold mb-6">Let's Connect</h3>
              <p className="text-gray-300 leading-relaxed mb-8">
                Feel free to reach out if you have any questions, want to
                collaborate on a project, or just want to say hello!
              </p>
            </div>

            <div className="space-y-6">
              <motion.div
                whileHover={{ x: 5 }}
                className="flex items-center space-x-4"
              >
                <div className="w-12 h-12 bg-sky-400/20 rounded-full flex items-center justify-center">
                  <Mail className="w-5 h-5 text-sky-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Email</p>
                  <p className="text-white"><EMAIL></p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ x: 5 }}
                className="flex items-center space-x-4"
              >
                <div className="w-12 h-12 bg-sky-400/20 rounded-full flex items-center justify-center">
                  <Github className="w-5 h-5 text-sky-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">GitHub</p>
                  <p className="text-white">github.com/swastiksaumya</p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ x: 5 }}
                className="flex items-center space-x-4"
              >
                <div className="w-12 h-12 bg-sky-400/20 rounded-full flex items-center justify-center">
                  <Linkedin className="w-5 h-5 text-sky-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">LinkedIn</p>
                  <p className="text-white">linkedin.com/in/swastiksaumya</p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ x: 5 }}
                className="flex items-center space-x-4"
              >
                <div className="w-12 h-12 bg-sky-400/20 rounded-full flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-sky-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Location</p>
                  <p className="text-white">Bihar, India</p>
                </div>
              </motion.div>
            </div>

            <div className="flex space-x-4 pt-4">
              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.1, y: -2 }}
                className="w-12 h-12 bg-sky-400 rounded-full flex items-center justify-center hover:bg-sky-500 transition-colors"
                aria-label="Email"
              >
                <Mail className="w-5 h-5" />
              </motion.a>
              <motion.a
                href="https://github.com/swastiksaumya"
                whileHover={{ scale: 1.1, y: -2 }}
                className="w-12 h-12 bg-sky-400 rounded-full flex items-center justify-center hover:bg-sky-500 transition-colors"
                aria-label="GitHub"
              >
                <Github className="w-5 h-5" />
              </motion.a>
              <motion.a
                href="https://linkedin.com/in/swastiksaumya"
                whileHover={{ scale: 1.1, y: -2 }}
                className="w-12 h-12 bg-sky-400 rounded-full flex items-center justify-center hover:bg-sky-500 transition-colors"
                aria-label="LinkedIn"
              >
                <Linkedin className="w-5 h-5" />
              </motion.a>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="bg-slate-800 border-slate-700 shadow-2xl">
              <CardHeader>
                <CardTitle className="text-white flex items-center space-x-2">
                  <Heart className="w-5 h-5 text-sky-400" />
                  <span>Send a Message</span>
                </CardTitle>
                <CardDescription className="text-slate-300">
                  I'll get back to you as soon as possible!
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-300 mb-2"
                    >
                      Name
                    </label>
                    <Input
                      id="name"
                      placeholder="Your name"
                      className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 focus:border-sky-400"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-300 mb-2"
                    >
                      Email
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 focus:border-sky-400"
                    />
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="subject"
                    className="block text-sm font-medium text-gray-300 mb-2"
                  >
                    Subject
                  </label>
                  <Input
                    id="subject"
                    placeholder="What's this about?"
                    className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 focus:border-sky-400"
                  />
                </div>
                <div>
                  <label
                    htmlFor="message"
                    className="block text-sm font-medium text-gray-300 mb-2"
                  >
                    Message
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Your message..."
                    rows={5}
                    className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 focus:border-sky-400"
                  />
                </div>
                <Button className="w-full bg-sky-400 hover:bg-sky-500 text-white font-medium py-3">
                  Send Message
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <HeroSection />
      <AboutSection />
      <SkillsSection />
      <ProjectsPreviewSection />
      <ContactSection />
      <Footer />
    </motion.div>
  );
}
