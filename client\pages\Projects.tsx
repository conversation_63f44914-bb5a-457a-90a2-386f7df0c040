import { motion } from "framer-motion";
import { useState } from "react";
import {
  <PERSON><PERSON>er<PERSON>pen,
  Github,
  ExternalLink,
  Code,
  Palette,
  Database,
  Globe,
  ArrowLeft,
  Filter,
  Star,
  Calendar,
  Users,
} from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function Projects() {
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Page transition animation
  const pageVariants = {
    initial: { opacity: 0, x: 20 },
    in: { opacity: 1, x: 0 },
    out: { opacity: 0, x: -20 },
  };

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.5,
  };

  // Project categories
  const categories = [
    {
      id: "all",
      name: "All Projects",
      icon: <FolderOpen className="w-4 h-4" />,
    },
    { id: "web", name: "Web Development", icon: <Globe className="w-4 h-4" /> },
    { id: "ai", name: "AI/ML", icon: <Database className="w-4 h-4" /> },
    { id: "ui", name: "UI/UX", icon: <Palette className="w-4 h-4" /> },
  ];

  // Projects data
  const projects = [
    {
      id: 1,
      title: "Gait Recognition System",
      description:
        "Advanced computer vision project using OpenCV and machine learning algorithms for human gait analysis and pattern recognition. Features real-time video processing and classification.",
      longDescription:
        "This project implements a comprehensive gait recognition system that can identify individuals based on their walking patterns. Using computer vision techniques and machine learning, the system processes video input to extract gait features and classify different walking patterns. The application has potential uses in security systems, medical diagnosis, and accessibility applications.",
      category: "ai",
      technologies: [
        "Python",
        "OpenCV",
        "Machine Learning",
        "NumPy",
        "Scikit-learn",
        "Computer Vision",
      ],
      features: [
        "Real-time video processing",
        "Gait feature extraction",
        "Pattern classification",
        "Multiple camera support",
        "Database integration",
        "Performance analytics",
      ],
      github: "https://github.com/swastiksaumya/gait-recognition",
      demo: "#",
      image: "/api/placeholder/600/400",
      status: "Completed",
      difficulty: "Advanced",
      duration: "3 months",
      team: "Solo Project",
      highlights: [
        "Achieved 87% accuracy in gait classification",
        "Processed over 1000 video samples",
        "Implemented custom feature extraction algorithms",
      ],
    },
    {
      id: 2,
      title: "Resume Optimizer",
      description:
        "AI-powered web application that analyzes resumes for ATS compatibility and provides intelligent suggestions for improvement using natural language processing.",
      longDescription:
        "An intelligent resume optimization tool that helps job seekers improve their resumes for better ATS (Applicant Tracking System) compatibility. The application uses AI to analyze resume content, structure, and formatting, providing actionable feedback and suggestions for improvement.",
      category: "ai",
      technologies: [
        "React",
        "TypeScript",
        "Node.js",
        "AI/ML APIs",
        "Tailwind CSS",
        "MongoDB",
      ],
      features: [
        "ATS compatibility analysis",
        "Keyword optimization suggestions",
        "Format and structure recommendations",
        "Industry-specific insights",
        "Before/after comparisons",
        "Export to multiple formats",
      ],
      github: "https://github.com/swastiksaumya/resume-optimizer",
      demo: "https://resume-optimizer-demo.netlify.app",
      image: "/api/placeholder/600/400",
      status: "In Progress",
      difficulty: "Intermediate",
      duration: "2 months",
      team: "Solo Project",
      highlights: [
        "Integrated multiple AI APIs for content analysis",
        "Built responsive React interface",
        "Implemented real-time feedback system",
      ],
    },
    {
      id: 3,
      title: "Real-Time Weather Dashboard",
      description:
        "Comprehensive weather application featuring real-time updates, interactive maps, detailed forecasts, and beautiful data visualizations with location-based services.",
      longDescription:
        "A feature-rich weather dashboard that provides comprehensive weather information including current conditions, hourly and weekly forecasts, weather maps, and detailed analytics. The application features a clean, intuitive interface with interactive charts and location-based services.",
      category: "web",
      technologies: [
        "JavaScript",
        "Chart.js",
        "Weather APIs",
        "CSS3",
        "HTML5",
        "Geolocation API",
      ],
      features: [
        "Real-time weather updates",
        "Interactive weather maps",
        "7-day detailed forecasts",
        "Location-based services",
        "Weather alerts and notifications",
        "Historical weather data",
      ],
      github: "https://github.com/swastiksaumya/weather-dashboard",
      demo: "https://weather-dashboard-swastik.netlify.app",
      image: "/api/placeholder/600/400",
      status: "Completed",
      difficulty: "Intermediate",
      duration: "1.5 months",
      team: "Solo Project",
      highlights: [
        "Integrated multiple weather APIs",
        "Built responsive design with CSS Grid",
        "Implemented geolocation services",
      ],
    },
    {
      id: 4,
      title: "Interactive Portfolio Website",
      description:
        "Modern, responsive portfolio website with dark mode, smooth animations, accessibility features, and advanced performance optimizations.",
      longDescription:
        "A cutting-edge portfolio website showcasing modern web development practices. Features include dark/light mode toggle, smooth animations, responsive design, accessibility compliance, and performance optimization techniques. Built with the latest web technologies and best practices.",
      category: "web",
      technologies: [
        "React",
        "TypeScript",
        "Tailwind CSS",
        "Framer Motion",
        "Vite",
        "ShadCN UI",
      ],
      features: [
        "Dark/Light mode toggle",
        "Smooth page transitions",
        "Responsive design",
        "Accessibility compliant",
        "SEO optimized",
        "Performance optimized",
      ],
      github: "https://github.com/swastiksaumya/portfolio",
      demo: "https://swastiksaumya.dev",
      image: "/api/placeholder/600/400",
      status: "Completed",
      difficulty: "Intermediate",
      duration: "1 month",
      team: "Solo Project",
      highlights: [
        "Achieved 95+ Lighthouse score",
        "Implemented advanced animations",
        "Built with accessibility in mind",
      ],
    },
    {
      id: 5,
      title: "Task Management System",
      description:
        "Full-stack productivity application with drag-and-drop functionality, real-time collaboration, user authentication, and team management features.",
      longDescription:
        "A comprehensive task management system designed for team collaboration and productivity. Features include drag-and-drop task organization, real-time updates, user authentication, team management, and detailed project analytics. Built with modern full-stack technologies.",
      category: "web",
      technologies: [
        "React",
        "Node.js",
        "Express",
        "MongoDB",
        "Socket.io",
        "JWT",
      ],
      features: [
        "Drag-and-drop interface",
        "Real-time collaboration",
        "User authentication",
        "Team management",
        "Project analytics",
        "Mobile responsive",
      ],
      github: "https://github.com/swastiksaumya/task-manager",
      demo: "#",
      image: "/api/placeholder/600/400",
      status: "In Progress",
      difficulty: "Advanced",
      duration: "4 months",
      team: "Solo Project",
      highlights: [
        "Implemented real-time features with Socket.io",
        "Built RESTful API with Express",
        "Designed responsive drag-and-drop interface",
      ],
    },
    {
      id: 6,
      title: "E-commerce Landing Page",
      description:
        "Modern, conversion-optimized landing page for e-commerce business with advanced animations, product showcase, and integrated contact forms.",
      longDescription:
        "A high-converting e-commerce landing page designed during my internship at WebBoost Solutions. Features modern design principles, smooth animations, product showcase sections, customer testimonials, and integrated contact forms. Optimized for conversion and mobile experience.",
      category: "ui",
      technologies: [
        "HTML5",
        "CSS3",
        "JavaScript",
        "Bootstrap",
        "GSAP",
        "Responsive Design",
      ],
      features: [
        "Conversion-optimized design",
        "Product showcase gallery",
        "Customer testimonials",
        "Contact form integration",
        "Mobile-first approach",
        "Performance optimized",
      ],
      github: "#",
      demo: "#",
      image: "/api/placeholder/600/400",
      status: "Completed",
      difficulty: "Beginner",
      duration: "3 weeks",
      team: "Internship Project",
      highlights: [
        "Increased conversion rate by 25%",
        "Implemented smooth scroll animations",
        "Achieved perfect mobile responsiveness",
      ],
    },
  ];

  // Filter projects based on selected category
  const filteredProjects =
    selectedCategory === "all"
      ? projects
      : projects.filter((project) => project.category === selectedCategory);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300";
      case "In Progress":
        return "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300";
      default:
        return "bg-gray-100 text-gray-600 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Beginner":
        return "bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300";
      case "Intermediate":
        return "bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-300";
      case "Advanced":
        return "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-600 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants}
      transition={pageTransition}
      className="min-h-screen bg-white dark:bg-slate-900"
    >
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-900">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Link to="/">
              <Button
                variant="ghost"
                className="mb-6 text-slate-600 dark:text-slate-300"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>

            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center">
              <FolderOpen className="w-10 h-10 text-white" />
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-6">
              My Projects
            </h1>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
              A collection of projects showcasing my journey in web development,
              AI/ML, and design. Each project represents learning, growth, and
              practical application of modern technologies.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={
                  selectedCategory === category.id ? "default" : "outline"
                }
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 ${
                  selectedCategory === category.id
                    ? "bg-purple-500 hover:bg-purple-600"
                    : ""
                }`}
              >
                {category.icon}
                <span>{category.name}</span>
              </Button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            layout
            className="grid md:grid-cols-2 xl:grid-cols-3 gap-8"
          >
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Card className="h-full shadow-lg hover:shadow-2xl transition-all duration-300 dark:bg-slate-800 border-none overflow-hidden group">
                  {/* Project Image/Placeholder */}
                  <div className="relative h-48 bg-gradient-to-br from-purple-400 to-pink-500 overflow-hidden">
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Code className="w-16 h-16 text-white opacity-50" />
                    </div>
                    {/* Status badges */}
                    <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                      <Badge className={getStatusColor(project.status)}>
                        {project.status}
                      </Badge>
                      <Badge className={getDifficultyColor(project.difficulty)}>
                        {project.difficulty}
                      </Badge>
                    </div>
                  </div>

                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between mb-2">
                      <CardTitle className="text-xl text-slate-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                        {project.title}
                      </CardTitle>
                      <div className="flex items-center space-x-1 text-yellow-400">
                        <Star className="w-4 h-4 fill-current" />
                      </div>
                    </div>
                    <CardDescription className="text-slate-600 dark:text-slate-300 line-clamp-3">
                      {project.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2">
                      {project.technologies
                        .slice(0, 4)
                        .map((tech, techIndex) => (
                          <Badge
                            key={techIndex}
                            variant="outline"
                            className="text-xs dark:border-slate-600 dark:text-slate-300"
                          >
                            {tech}
                          </Badge>
                        ))}
                      {project.technologies.length > 4 && (
                        <Badge
                          variant="outline"
                          className="text-xs dark:border-slate-600 dark:text-slate-400"
                        >
                          +{project.technologies.length - 4}
                        </Badge>
                      )}
                    </div>

                    {/* Project Info */}
                    <div className="grid grid-cols-2 gap-4 text-sm text-slate-500 dark:text-slate-400">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>{project.duration}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4" />
                        <span>{project.team}</span>
                      </div>
                    </div>

                    {/* Key Highlights */}
                    <div>
                      <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">
                        Key Highlights:
                      </h4>
                      <ul className="space-y-1">
                        {project.highlights
                          .slice(0, 2)
                          .map((highlight, index) => (
                            <li
                              key={index}
                              className="text-xs text-slate-600 dark:text-slate-300 flex items-start space-x-2"
                            >
                              <div className="w-1 h-1 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                              <span>{highlight}</span>
                            </li>
                          ))}
                      </ul>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3 pt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex items-center space-x-2 flex-1 dark:border-slate-600"
                        disabled={project.github === "#"}
                      >
                        <Github className="w-4 h-4" />
                        <span>Code</span>
                      </Button>
                      <Button
                        size="sm"
                        className="bg-purple-500 hover:bg-purple-600 flex items-center space-x-2 flex-1"
                        disabled={project.demo === "#"}
                      >
                        <ExternalLink className="w-4 h-4" />
                        <span>Demo</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* No projects message */}
          {filteredProjects.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <FolderOpen className="w-16 h-16 mx-auto text-slate-400 mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 dark:text-slate-300 mb-2">
                No projects found
              </h3>
              <p className="text-slate-500 dark:text-slate-400">
                Try selecting a different category to see more projects.
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-800 dark:to-slate-900">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Let's Build Something Together
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed mb-8">
              I'm always excited to work on new projects and collaborate with
              fellow developers. Have an idea? Let's discuss how we can bring it
              to life!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/#contact">
                <Button size="lg" className="bg-purple-500 hover:bg-purple-600">
                  Get In Touch
                </Button>
              </Link>
              <Button size="lg" variant="outline">
                View Resume
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  );
}
