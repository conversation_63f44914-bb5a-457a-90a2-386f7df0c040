@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Portfolio color scheme
   * Using the specified palette: #0F172A, #1E293B, #38BDF8, white
   */
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 7%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 7%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 7%;

    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 100%;

    --secondary: 215 25% 17%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 13% 91%;
    --muted-foreground: 215 14% 46%;

    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 199 89% 48%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 215 28% 6%;
    --foreground: 0 0% 100%;

    --card: 215 25% 17%;
    --card-foreground: 0 0% 100%;

    --popover: 215 25% 17%;
    --popover-foreground: 0 0% 100%;

    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 100%;

    --secondary: 215 25% 17%;
    --secondary-foreground: 0 0% 100%;

    --muted: 215 25% 17%;
    --muted-foreground: 220 9% 65%;

    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62% 31%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 25% 17%;
    --input: 215 25% 17%;
    --ring: 199 89% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
