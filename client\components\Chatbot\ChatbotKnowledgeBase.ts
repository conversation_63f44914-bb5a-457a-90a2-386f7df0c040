// Comprehensive knowledge base for Swastik <PERSON>'s portfolio chatbot

export interface KnowledgeEntry {
  keywords: string[];
  response: string;
  category: string;
  followUp?: string[];
}

export const portfolioKnowledge: KnowledgeEntry[] = [
  // Personal Information
  {
    keywords: ["who", "swastik", "name", "about", "introduce"],
    response: "Hi! I'm <PERSON><PERSON><PERSON><PERSON>, a passionate BCA student and beginner web developer. I love building responsive and animated websites with modern technologies like React, TypeScript, and Tailwind CSS. I'm currently learning full-stack development and exploring AI/ML.",
    category: "personal",
    followUp: ["Would you like to know about my education?", "Want to see my projects?", "Interested in my internship experience?"]
  },

  // Education
  {
    keywords: ["education", "bca", "college", "university", "degree", "study", "student"],
    response: "I'm currently pursuing Bachelor of Computer Applications (BCA) at XYZ University in Bihar, India (2023-2026). I maintain a CGPA of 8.2/10. I also completed my 12th grade in Science stream from ABC School with 78%. My coursework includes Data Structures, Algorithms, Web Development, and Database Management.",
    category: "education",
    followUp: ["What subjects am I studying?", "Tell me about my academic projects", "What are my future learning goals?"]
  },

  {
    keywords: ["subjects", "curriculum", "coursework", "learning"],
    response: "My BCA curriculum covers: Data Structures & Algorithms, Database Management Systems, Web Development, Software Engineering, Computer Networks, Operating Systems, Object-Oriented Programming, and System Analysis & Design. I'm also learning modern technologies like React, Node.js, and AI/ML fundamentals.",
    category: "education"
  },

  // Internship
  {
    keywords: ["internship", "webboost", "experience", "work", "job", "intern"],
    response: "I completed a 3-month remote internship as a Front-End Developer at WebBoost Solutions by UM (June-August 2023). I worked on building responsive websites, improving UI/UX, and collaborating with a remote team using Git. I delivered 6+ projects and significantly improved my React and JavaScript skills.",
    category: "internship",
    followUp: ["What projects did I work on?", "What technologies did I use?", "What did I learn from the internship?"]
  },

  {
    keywords: ["webboost", "projects", "internship projects", "work projects"],
    response: "During my internship, I worked on several projects: 1) E-commerce Landing Page (improved conversion by 25%), 2) Business Portfolio Website with React, and 3) Restaurant Website Redesign (increased mobile engagement by 40%). I used HTML, CSS, JavaScript, React, and responsive design principles.",
    category: "internship"
  },

  // Projects
  {
    keywords: ["projects", "portfolio", "github", "code", "development"],
    response: "I've built several exciting projects: 1) Gait Recognition System using OpenCV and ML (87% accuracy), 2) Resume Optimizer with AI-powered suggestions, 3) Real-Time Weather Dashboard with interactive maps, 4) This Interactive Portfolio with dark mode and animations, and 5) Task Management System with drag-and-drop features.",
    category: "projects",
    followUp: ["Tell me about the Gait Recognition project", "What's the Resume Optimizer?", "Show me the Weather Dashboard details"]
  },

  {
    keywords: ["gait", "recognition", "opencv", "machine learning", "ai"],
    response: "The Gait Recognition System is an advanced computer vision project using OpenCV and machine learning. It analyzes human walking patterns for identification with 87% accuracy. I processed over 1000 video samples and implemented custom feature extraction algorithms. It has applications in security systems and medical diagnosis.",
    category: "projects"
  },

  {
    keywords: ["resume", "optimizer", "ats"],
    response: "The Resume Optimizer is an AI-powered tool that analyzes resumes for ATS compatibility. It provides intelligent suggestions for keyword optimization, format improvements, and structure recommendations. Built with React, TypeScript, and AI APIs, it helps job seekers improve their resumes for better recruitment success.",
    category: "projects"
  },

  {
    keywords: ["weather", "dashboard", "api"],
    response: "The Weather Dashboard provides comprehensive weather information with real-time updates, interactive maps, 7-day forecasts, and beautiful data visualizations. It features location-based services, weather alerts, and historical data. Built with JavaScript, Chart.js, and multiple weather APIs.",
    category: "projects"
  },

  // Skills & Technologies
  {
    keywords: ["skills", "technologies", "tech stack", "programming", "languages"],
    response: "My technical skills include: Frontend - HTML, CSS, JavaScript, React, TypeScript, Tailwind CSS (85-90% proficiency); Backend - Node.js, Express, MongoDB (60-65% proficiency); Tools - Git, Figma, Framer Motion (70-80% proficiency). I'm also learning AI/ML, cloud computing, and DevOps.",
    category: "skills",
    followUp: ["What's my strongest skill?", "What am I currently learning?", "Tell me about my design skills"]
  },

  {
    keywords: ["react", "javascript", "frontend"],
    response: "I'm proficient in React and JavaScript! I've built multiple projects using React including this portfolio, the Resume Optimizer, and several projects during my internship. I understand component architecture, hooks, state management, and modern React patterns. My JavaScript skills include ES6+, async/await, and API integration.",
    category: "skills"
  },

  // Contact & Social
  {
    keywords: ["contact", "email", "linkedin", "github", "social"],
    response: "You can reach me at: Email: <EMAIL>, GitHub: github.com/swastiksaumya, LinkedIn: linkedin.com/in/swastiksaumya. I'm located in Bihar, India. I'm always open to discussing new opportunities, collaborations, and exciting projects!",
    category: "contact"
  },

  // Goals & Future
  {
    keywords: ["goals", "future", "plans", "aspirations", "career"],
    response: "My future goals include: mastering full-stack development (MERN stack), learning cloud technologies and DevOps, exploring AI/ML applications, contributing to open-source projects, and eventually becoming a senior full-stack developer. I'm excited about building scalable applications and working on innovative tech solutions.",
    category: "future"
  },

  // Help & Navigation
  {
    keywords: ["help", "what can you do", "commands", "features"],
    response: "I can help you learn about Swastik's background, education, internship experience, projects, skills, and contact information. Try asking me: 'Tell me about his projects', 'What's his education?', 'Show me his skills', 'How to contact him?', or 'What are his future goals?'",
    category: "help"
  },

  // Portfolio Features
  {
    keywords: ["website", "portfolio", "features", "dark mode", "animations"],
    response: "This portfolio is built with React, TypeScript, Tailwind CSS, and Framer Motion. It features dark/light mode toggle, smooth animations, responsive design, multi-page routing, SEO optimization, and now this AI chatbot! It showcases modern web development practices and interactive user experience.",
    category: "portfolio"
  }
];

// Intent recognition patterns
export const intentPatterns = {
  greeting: /^(hi|hello|hey|good morning|good afternoon|good evening)/i,
  thanks: /^(thanks|thank you|appreciate)/i,
  goodbye: /^(bye|goodbye|see you|farewell)/i,
  question: /^(what|how|when|where|why|who|can|do|does|is|are)/i
};

// Response templates
export const responseTemplates = {
  greeting: [
    "Hello! 👋 I'm Swastik's AI assistant. I can tell you all about his background, projects, skills, and experience. What would you like to know?",
    "Hi there! 🚀 Welcome to Swastik's portfolio. I'm here to help you learn about his journey in web development. Ask me anything!",
    "Hey! 🌟 I'm the AI assistant for this portfolio. I have comprehensive knowledge about Swastik's education, internship, projects, and skills. How can I help you?"
  ],
  thanks: [
    "You're welcome! 😊 Is there anything else you'd like to know about Swastik's background or projects?",
    "Happy to help! 🙌 Feel free to ask me about his skills, education, or any specific projects.",
    "Glad I could assist! 💫 Want to explore more about his internship experience or future goals?"
  ],
  goodbye: [
    "Goodbye! 👋 Thanks for exploring Swastik's portfolio. Feel free to return anytime!",
    "See you later! 🌟 Don't forget to check out his projects and get in touch if you're interested in collaboration.",
    "Farewell! 🚀 Remember, Swastik is always open to new opportunities and exciting projects."
  ],
  fallback: [
    "I'm not sure about that specific topic. Could you ask about Swastik's education, projects, internship, skills, or contact information?",
    "Hmm, I don't have information on that. Try asking me about his background, experience, or technical skills!",
    "I might not have understood that correctly. Ask me about his projects, education, internship experience, or future goals!"
  ]
};

// Smart search function
export function findRelevantResponse(userInput: string): string {
  const input = userInput.toLowerCase();
  
  // Check for greeting
  if (intentPatterns.greeting.test(input)) {
    return responseTemplates.greeting[Math.floor(Math.random() * responseTemplates.greeting.length)];
  }
  
  // Check for thanks
  if (intentPatterns.thanks.test(input)) {
    return responseTemplates.thanks[Math.floor(Math.random() * responseTemplates.thanks.length)];
  }
  
  // Check for goodbye
  if (intentPatterns.goodbye.test(input)) {
    return responseTemplates.goodbye[Math.floor(Math.random() * responseTemplates.goodbye.length)];
  }
  
  // Search knowledge base
  let bestMatch: KnowledgeEntry | null = null;
  let maxScore = 0;
  
  portfolioKnowledge.forEach(entry => {
    let score = 0;
    entry.keywords.forEach(keyword => {
      if (input.includes(keyword.toLowerCase())) {
        score += keyword.length; // Longer keywords get higher score
      }
    });
    
    if (score > maxScore) {
      maxScore = score;
      bestMatch = entry;
    }
  });
  
  if (bestMatch && maxScore > 0) {
    return bestMatch.response;
  }
  
  // Fallback response
  return responseTemplates.fallback[Math.floor(Math.random() * responseTemplates.fallback.length)];
}

// Get follow-up suggestions
export function getFollowUpSuggestions(userInput: string): string[] {
  const input = userInput.toLowerCase();
  
  for (const entry of portfolioKnowledge) {
    if (entry.keywords.some(keyword => input.includes(keyword.toLowerCase()))) {
      return entry.followUp || [];
    }
  }
  
  return [
    "Tell me about his projects",
    "What's his education background?",
    "Show me his skills",
    "How can I contact him?"
  ];
}

// Conversation context management
export class ConversationContext {
  private history: Array<{ user: string; bot: string; timestamp: Date }> = [];
  
  addExchange(userMessage: string, botResponse: string) {
    this.history.push({
      user: userMessage,
      bot: botResponse,
      timestamp: new Date()
    });
    
    // Keep only last 10 exchanges
    if (this.history.length > 10) {
      this.history = this.history.slice(-10);
    }
  }
  
  getHistory() {
    return this.history;
  }
  
  getLastTopic(): string | null {
    if (this.history.length === 0) return null;
    
    const lastExchange = this.history[this.history.length - 1];
    
    // Analyze the last bot response to determine topic
    for (const entry of portfolioKnowledge) {
      if (lastExchange.bot.includes(entry.response.substring(0, 50))) {
        return entry.category;
      }
    }
    
    return null;
  }
  
  clear() {
    this.history = [];
  }
}
