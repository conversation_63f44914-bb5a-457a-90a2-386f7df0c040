<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    <title>
      Swastik Saumya - Beginner Web Developer | BCA Student | Portfolio
    </title>
    <meta
      name="description"
      content="<PERSON><PERSON><PERSON><PERSON> is a passionate BCA student and beginner web developer skilled in React, JavaScript, and modern web technologies. View my projects and journey in frontend development."
    /> 
    <meta
      name="keywords"
      content="<PERSON><PERSON><PERSON><PERSON>, web developer, frontend developer, BCA student, React developer, JavaScript, portfolio, web development, Bihar"
    />
    <meta name="author" content="Swastik Saumya" />
    <meta name="robots" content="index, follow" />

    <!-- OpenGraph Meta Tags -->
    <meta 
      property="og:title"
      content="Swastik Saumya - Beginner Web Developer | Portfolio"
    />
    <meta
      property="og:description"
      content="Passionate BCA student and beginner web developer building responsive websites with React, JavaScript, and modern technologies."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://swastiksaumya.dev" />
    <meta property="og:image" content="/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Swastik Saumya Portfolio" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Swastik Saumya - Beginner Web Developer"
    />
    <meta
      name="twitter:description"
      content="Passionate BCA student building responsive websites with modern technologies."
    />
    <meta name="twitter:image" content="/og-image.jpg" />

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#38BDF8" />
    <meta name="msapplication-TileColor" content="#38BDF8" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>

  <body>
    <div id="root"></div>

    <script type="module" src="/client/App.tsx"></script>
  </body>
</html>
